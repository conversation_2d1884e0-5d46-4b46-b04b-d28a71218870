import React from "react";
import { <PERSON>, NavLink, useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { useClub } from "Context/Club";
import DownloadAppModal from "Components/DownloadAppModal";

let sdk = new MkdSDK();

export const NAV_ITEMS = [
  {
    to: "/user/dashboard",
    text: "Home",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.9868 2.5789L10.5119 1.99843V1.99843L10.9868 2.5789ZM11.7291 2.07937L11.9323 2.80134L11.7291 2.07937ZM13.0132 2.5789L13.4881 1.99843L13.0132 2.5789ZM12.2709 2.07937L12.0677 2.80134L12.2709 2.07937ZM4.29601 20.1409L4.6365 19.4727L4.29601 20.1409ZM3.75 18.6499H4.5H3.75ZM3.85899 19.7039L4.52725 19.3634L3.85899 19.7039ZM20.141 19.7039L19.4728 19.3634L20.141 19.7039ZM19.704 20.1409L19.3635 19.4727L19.704 20.1409ZM20.215 8.7114L20.9387 8.51459L20.215 8.7114ZM19.6632 8.01981L19.1883 8.60028L19.6632 8.01981ZM20.0641 8.39314L19.4535 8.82864L20.0641 8.39314ZM3.75 9.25814H3H3.75ZM3.78504 8.7114L3.06133 8.51459H3.06133L3.78504 8.7114ZM4.33682 8.01981L4.81175 8.60027L4.33682 8.01981ZM3.93586 8.39314L4.54646 8.82864L3.93586 8.39314ZM7.75 15.4999C7.33579 15.4999 7 15.8357 7 16.2499C7 16.6641 7.33579 16.9999 7.75 16.9999V15.4999ZM16.25 16.9999C16.6642 16.9999 17 16.6641 17 16.2499C17 15.8357 16.6642 15.4999 16.25 15.4999V16.9999ZM19.5 9.25814V18.6499H21V9.25814H19.5ZM18.65 19.4999H5.35V20.9999H18.65V19.4999ZM4.5 18.6499V9.25814H3V18.6499H4.5ZM4.81175 8.60027L11.4617 3.15936L10.5119 1.99843L3.86189 7.43934L4.81175 8.60027ZM12.5383 3.15937L19.1883 8.60028L20.1381 7.43934L13.4881 1.99843L12.5383 3.15937ZM11.4617 3.15936C11.6508 3.00471 11.7614 2.91484 11.8487 2.85362C11.9282 2.79783 11.9449 2.79778 11.9323 2.80134L11.526 1.35741C11.3125 1.41749 11.1385 1.51954 10.9874 1.62554C10.844 1.72612 10.6838 1.85775 10.5119 1.99843L11.4617 3.15936ZM13.4881 1.99843C13.3162 1.85775 13.156 1.72612 13.0126 1.62554C12.8615 1.51954 12.6875 1.41749 12.474 1.35741L12.0677 2.80134C12.0551 2.79778 12.0718 2.79783 12.1513 2.85362C12.2386 2.91484 12.3492 3.00471 12.5383 3.15937L13.4881 1.99843ZM11.9323 2.80134C11.9766 2.78888 12.0234 2.78888 12.0677 2.80134L12.474 1.35741C12.164 1.27019 11.836 1.27019 11.526 1.35741L11.9323 2.80134ZM5.35 19.4999C5.0576 19.4999 4.8834 19.4993 4.75428 19.4888C4.6339 19.479 4.62011 19.4643 4.6365 19.4727L3.95552 20.8092C4.18582 20.9265 4.419 20.9664 4.63213 20.9838C4.83651 21.0005 5.08235 20.9999 5.35 20.9999V19.4999ZM3 18.6499C3 18.9176 2.99942 19.1634 3.01612 19.3678C3.03353 19.5809 3.07339 19.8141 3.19074 20.0444L4.52725 19.3634C4.5356 19.3798 4.52097 19.366 4.51113 19.2457C4.50058 19.1165 4.5 18.9423 4.5 18.6499H3ZM4.6365 19.4727C4.58946 19.4487 4.55122 19.4105 4.52725 19.3634L3.19074 20.0444C3.35852 20.3737 3.62623 20.6414 3.95552 20.8092L4.6365 19.4727ZM19.5 18.6499C19.5 18.9423 19.4994 19.1165 19.4889 19.2457C19.479 19.366 19.4644 19.3798 19.4728 19.3634L20.8093 20.0444C20.9266 19.8141 20.9665 19.5809 20.9839 19.3678C21.0006 19.1634 21 18.9176 21 18.6499H19.5ZM18.65 20.9999C18.9177 20.9999 19.1635 21.0005 19.3679 20.9838C19.581 20.9664 19.8142 20.9265 20.0445 20.8092L19.3635 19.4727C19.3799 19.4643 19.3661 19.479 19.2457 19.4888C19.1166 19.4993 18.9424 19.4999 18.65 19.4999V20.9999ZM19.4728 19.3634C19.4488 19.4105 19.4105 19.4487 19.3635 19.4727L20.0445 20.8092C20.3738 20.6414 20.6415 20.3737 20.8093 20.0444L19.4728 19.3634ZM21 9.25814C21 9.02065 21.0062 8.76275 20.9387 8.51459L19.4912 8.90821C19.4898 8.90287 19.4938 8.91222 19.4966 8.96689C19.4998 9.02914 19.5 9.11151 19.5 9.25814H21ZM19.1883 8.60028C19.3017 8.69312 19.3653 8.74546 19.4115 8.78734C19.4521 8.82412 19.4567 8.83315 19.4535 8.82864L20.6747 7.95764C20.5254 7.74827 20.3219 7.58973 20.1381 7.43934L19.1883 8.60028ZM20.9387 8.51459C20.8843 8.31479 20.795 8.12622 20.6747 7.95764L19.4535 8.82864C19.4707 8.85272 19.4835 8.87966 19.4912 8.90821L20.9387 8.51459ZM4.5 9.25814C4.5 9.11151 4.50022 9.02914 4.5034 8.96689C4.5062 8.91222 4.51021 8.90287 4.50876 8.90821L3.06133 8.51459C2.99384 8.76275 3 9.02065 3 9.25814H4.5ZM3.86189 7.43934C3.67808 7.58973 3.47458 7.74827 3.32525 7.95764L4.54646 8.82864C4.54325 8.83314 4.54795 8.82412 4.58849 8.78734C4.63466 8.74546 4.69827 8.69312 4.81175 8.60027L3.86189 7.43934ZM4.50876 8.90821C4.51652 8.87966 4.52929 8.85273 4.54646 8.82864L3.32525 7.95764C3.20502 8.12622 3.11566 8.31479 3.06133 8.51459L4.50876 8.90821ZM7.75 16.9999H16.25V15.4999H7.75V16.9999Z"
          fill="#525866"
        />
      </svg>
    ),
    value: "dashboard",
  },
  {
    to: "/user/reserve-court",
    text: "Reserve a court",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.75 4.75V2.75M16.25 4.75V2.75M11.9991 9.75V15.25M14.75 12.5H9.25M4.75 20.25H19.25C19.8023 20.25 20.25 19.8023 20.25 19.25V5.75C20.25 5.19772 19.8023 4.75 19.25 4.75H4.75C4.19772 4.75 3.75 5.19772 3.75 5.75V19.25C3.75 19.8023 4.19772 20.25 4.75 20.25Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    value: "daily-scheduler",
  },
  {
    to: "/user/lessons",
    text: "Lessons",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.25 3.75H4.75C4.19772 3.75 3.75 4.19772 3.75 4.75V19.25C3.75 19.8023 4.19771 20.25 4.75 20.25H7.25647M20.25 12.75V19.25C20.25 19.8023 19.8023 20.25 19.25 20.25H16.7435M7.25647 20.25C7.38647 17.7429 9.46051 15.75 12 15.75C14.5395 15.75 16.6135 17.7429 16.7435 20.25M7.25647 20.25H16.7435M19 1.75L20.0833 3.91667L22.25 5L20.0833 6.08333L19 8.25L17.9167 6.08333L15.75 5L17.9167 3.91667L19 1.75ZM14.75 10.5C14.75 12.0188 13.5188 13.25 12 13.25C10.4812 13.25 9.25 12.0188 9.25 10.5C9.25 8.98122 10.4812 7.75 12 7.75C13.5188 7.75 14.75 8.98122 14.75 10.5Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    value: "lessons",
  },
  {
    to: "/user/program-clinics",
    text: "Program/Clinics",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.25 4.75H18.25C18.8023 4.75 19.25 5.19772 19.25 5.75V20.25C19.25 20.8023 18.8023 21.25 18.25 21.25H5.75C5.19771 21.25 4.75 20.8023 4.75 20.25V5.75C4.75 5.19772 5.19772 4.75 5.75 4.75H8.75M9.75 7.25H14.25C14.8023 7.25 15.25 6.80228 15.25 6.25V3.75C15.25 3.19772 14.8023 2.75 14.25 2.75H9.75C9.19772 2.75 8.75 3.19772 8.75 3.75V6.25C8.75 6.80228 9.19772 7.25 9.75 7.25Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="square"
          stroke-linejoin="round"
        />
      </svg>
    ),
    value: "program-clinics",
  },
  {
    to: "/user/find-a-buddy",
    text: "Find a Buddy",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19.5005 8.75V11.5M19.5005 11.5V14.25M19.5005 11.5H16.7505M19.5005 11.5H22.2505M14.7505 6.5C14.7505 8.57107 13.0716 10.25 11.0005 10.25C8.92942 10.25 7.25049 8.57107 7.25049 6.5C7.25049 4.42893 8.92942 2.75 11.0005 2.75C13.0716 2.75 14.7505 4.42893 14.7505 6.5ZM3.67204 19.1657C4.4344 15.7735 7.20488 13.25 11.0005 13.25C14.7961 13.25 17.5666 15.7735 18.3289 19.1657C18.4581 19.7406 17.9913 20.25 17.4021 20.25H4.59891C4.00965 20.25 3.54284 19.7406 3.67204 19.1657Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    value: "find-a-buddy",
  },

  // {
  //   to: "/club/availability",
  //   text: "Availability",
  //   icon: (
  //     <svg
  //       width="24"
  //       height="24"
  //       viewBox="0 0 24 24"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M8.75 20.25H4.75C4.19772 20.25 3.75 19.8023 3.75 19.25V5.75C3.75 5.19772 4.19772 4.75 4.75 4.75H19.25C19.8023 4.75 20.25 5.19772 20.25 5.75V8.75M7.75 4.75V2.75M16.25 4.75V2.75M17 14.75V16.9996L18.75 18.75M14.9917 12.1509C17.6701 11.0415 20.7396 12.3137 21.8491 14.9919C22.9586 17.6702 21.6863 20.7396 19.0079 21.8491C16.3296 22.9585 13.2601 21.6863 12.1506 19.0081C11.0419 16.3306 12.3133 13.2604 14.9917 12.1509Z"
  //         stroke="#525866"
  //         stroke-width="1.5"
  //         stroke-linecap="round"
  //         stroke-linejoin="round"
  //       />
  //     </svg>
  //   ),
  //   value: "availability",
  // },

  {
    to: "/user/my-groups",
    text: "My Groups",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.75 19.25H21.423C22.0045 19.25 22.4683 18.754 22.3706 18.1808C21.8392 15.0624 20.0191 12.75 17 12.75C16.554 12.75 16.1341 12.8005 15.7407 12.8966M11.2499 7C11.2499 8.79493 9.79486 10.25 7.99993 10.25C6.205 10.25 4.74993 8.79493 4.74993 7C4.74993 5.20507 6.205 3.75 7.99993 3.75C9.79486 3.75 11.2499 5.20507 11.2499 7ZM19.7499 7.5C19.7499 9.01878 18.5187 10.25 16.9999 10.25C15.4811 10.25 14.2499 9.01878 14.2499 7.5C14.2499 5.98122 15.4811 4.75 16.9999 4.75C18.5187 4.75 19.7499 5.98122 19.7499 7.5ZM1.87053 19.1808C2.43845 15.4997 4.5171 12.75 7.99993 12.75C11.4828 12.75 13.5614 15.4997 14.1293 19.1808C14.2179 19.755 13.7552 20.25 13.1741 20.25H2.82572C2.24471 20.25 1.78194 19.755 1.87053 19.1808Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    value: "my-groups",
  },
  {
    to: "/user/my-reservations",
    text: "My Reservations",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M20.25 7.75V4.75C20.25 4.19772 19.8023 3.75 19.25 3.75H4.75C4.19772 3.75 3.75 4.19772 3.75 4.75V7.75M20.25 7.75V19.25C20.25 19.8023 19.8023 20.25 19.25 20.25H4.75C4.19772 20.25 3.75 19.8023 3.75 19.25V7.75M20.25 7.75H3.75M8 12V11.99M12 12V11.99M8 16V15.99M12 16V15.99M16 12V11.99M8.25 12C8.25 12.1381 8.13807 12.25 8 12.25C7.86193 12.25 7.75 12.1381 7.75 12C7.75 11.8619 7.86193 11.75 8 11.75C8.13807 11.75 8.25 11.8619 8.25 12ZM12.25 12C12.25 12.1381 12.1381 12.25 12 12.25C11.8619 12.25 11.75 12.1381 11.75 12C11.75 11.8619 11.8619 11.75 12 11.75C12.1381 11.75 12.25 11.8619 12.25 12ZM8.25 16C8.25 16.1381 8.13807 16.25 8 16.25C7.86193 16.25 7.75 16.1381 7.75 16C7.75 15.8619 7.86193 15.75 8 15.75C8.13807 15.75 8.25 15.8619 8.25 16ZM12.25 16C12.25 16.1381 12.1381 16.25 12 16.25C11.8619 16.25 11.75 16.1381 11.75 16C11.75 15.8619 11.8619 15.75 12 15.75C12.1381 15.75 12.25 15.8619 12.25 16ZM16.25 12C16.25 12.1381 16.1381 12.25 16 12.25C15.8619 12.25 15.75 12.1381 15.75 12C15.75 11.8619 15.8619 11.75 16 11.75C16.1381 11.75 16.25 11.8619 16.25 12Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="round"
        />
      </svg>
    ),
    value: "my-reservations",
  },
  {
    to: "/user/club-calendar",
    text: "Club Calendar",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.46184 9.95625L3.7642 17.3423C3.86011 17.8862 4.37877 18.2494 4.92266 18.1535L10.1159 17.2378M2.46184 9.95625L1.9409 7.00182C1.84499 6.45793 2.20816 5.93927 2.75206 5.84337L14.0773 3.84641C14.6212 3.75051 15.1399 4.11368 15.2358 4.65757L15.7567 7.612L2.46184 9.95625ZM16 11.7499V13.9999L18 15.9999M22.25 13.9999C22.25 17.4517 19.4518 20.2499 16 20.2499C12.5482 20.2499 9.75 17.4517 9.75 13.9999C9.75 10.5482 12.5482 7.74994 16 7.74994C19.4518 7.74994 22.25 10.5482 22.25 13.9999Z"
          stroke="#525866"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    value: "club-calendar",
  },

  // {
  //   to: "/club/profile",
  //   text: "Profile",
  //   icon: <PiUsersThreeFill className="text-xl text-[#A8A8A8]" />,
  //   value: "profile",
  // },
];

export const UserHeader = () => {
  const {
    state: { isOpen, path },
    dispatch: gobalDispatch,
  } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const navigate = useNavigate();
  const [showMobilePromo] = React.useState(true);
  const [isDownloadAppModalOpen, setIsDownloadAppModalOpen] =
    React.useState(false);
  const { club, user_permissions, club_membership, user_subscription } =
    useClub();
  const [filteredNavItems, setFilteredNavItems] = React.useState([]);
  const [isPermissionsLoading, setIsPermissionsLoading] = React.useState(true);

  console.log("user_permissions", user_permissions);
  // Filter NAV_ITEMS based on user_permissions
  React.useEffect(() => {
    if (!user_permissions) {
      // Keep loading state until permissions are loaded
      setIsPermissionsLoading(true);
      return;
    }

    const permissionMap = {
      "daily-scheduler": user_permissions.allowCourt, // Reserve a court
      lessons: user_permissions.allowCoach, // Lessons
      "program-clinics": user_permissions.allowClinic, // Program/Clinics
      "find-a-buddy": user_permissions.allowBuddy, // Find a Buddy
      "my-groups": user_permissions.allowGroups, // My Groups
      // Always visible items (no permission check needed)
      dashboard: true, // Home
      "my-reservations": true, // My Reservations
      "club-calendar": true, // Club Calendar
    };

    const filtered = NAV_ITEMS.filter((item) => {
      const hasPermission = permissionMap[item.value];
      return hasPermission !== false; // Show if true or undefined
    });

    setFilteredNavItems(filtered);
    setIsPermissionsLoading(false);
  }, [user_permissions]);

  let toggleOpen = (open) => {
    gobalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  React.useEffect(() => {
    async function fetchData() {
      try {
        const result = await sdk.getProfile();
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(
          dispatch,
          error.response.data.message
            ? error.response.data.message
            : error.message
        );
      }
    }
    fetchData();
  }, []);
  return (
    <div
      className={`sticky left-0 top-0 z-50 flex h-screen flex-col bg-white shadow-lg transition-all duration-300 ${
        isOpen ? "w-64" : "w-20"
      }`}
    >
      <div className="relative flex h-full flex-col overflow-y-auto">
        {/* Logo Section */}
        <div className="flex h-16 items-center justify-between border-b px-4">
          {isOpen && (
            <Link
              className="flex cursor-pointer items-center gap-1 px-4 py-4 "
              to="/"
            >
              {club?.club_logo ? (
                <img src={club?.club_logo} className="h-6 w-6" alt="" />
              ) : (
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2 12C2 9.28189 3.08445 6.81707 4.84428 5.0146C7.05362 6.54963 8.5 9.10591 8.5 12C8.5 14.8941 7.05362 17.4504 4.84428 18.9854C3.08445 17.1829 2 14.7181 2 12Z"
                    fill="black"
                  />
                  <path
                    d="M10 12C10 8.72836 8.42889 5.82368 6 3.99927C7.67132 2.74389 9.74879 2 12 2C14.2512 2 16.3287 2.74389 18 3.99927C15.5711 5.82368 14 8.72837 14 12C14 15.2716 15.5711 18.1763 18 20.0007C16.3287 21.2561 14.2512 22 12 22C9.74879 22 7.67132 21.2561 6 20.0007C8.42889 18.1763 10 15.2716 10 12Z"
                    fill="black"
                  />
                  <path
                    d="M19.1557 5.0146C20.9156 6.81707 22 9.28189 22 12C22 14.7181 20.9156 17.1829 19.1557 18.9854C16.9464 17.4504 15.5 14.8941 15.5 12C15.5 9.10591 16.9464 6.54963 19.1557 5.0146Z"
                    fill="black"
                  />
                </svg>
              )}
              <h4 className="font-sans font-bold">{club?.name || ""}</h4>
            </Link>
          )}
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 space-y-1 px-2 py-4">
          {isPermissionsLoading
            ? // Show skeleton loader while permissions are loading
              Array.from({ length: 7 }).map((_, index) => (
                <div
                  key={index}
                  className="flex animate-pulse items-center rounded-lg px-4 py-2"
                >
                  <div className="h-6 w-6 rounded bg-gray-200"></div>
                  {isOpen && (
                    <div className="ml-3 h-4 w-24 rounded bg-gray-200"></div>
                  )}
                </div>
              ))
            : filteredNavItems.map((item) => (
                <NavLink
                  key={item.value}
                  to={item.to}
                  className={({ isActive }) =>
                    `flex items-center rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
                      isActive
                        ? "bg-primary-50 text-primary-600"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`
                  }
                >
                  <span className="flex items-center">
                    {React.cloneElement(item.icon, {
                      className: `${item.icon.props.className} ${
                        path === item.value
                          ? "text-primary-600"
                          : "text-gray-400"
                      }`,
                    })}
                    {isOpen && <span className="ml-3">{item.text}</span>}
                  </span>
                </NavLink>
              ))}
        </nav>

        {/* Support Chat and Mobile App buttons */}
        {isOpen && showMobilePromo && (
          <div className="px-4 pt-4">
            {/* Support Chat Button */}
            {/* <div className="mb-3">
              <SupportChatBot />
            </div> */}

            {/* Mobile App Button */}
            <div className="mb-[7rem] rounded-lg bg-gray-100 p-3 shadow-sm">
              <div className="mb-2 flex flex-col items-start gap-2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.75 19.25H14.25M6.75 22.25H17.25C17.8023 22.25 18.25 21.8023 18.25 21.25V2.75C18.25 2.19772 17.8023 1.75 17.25 1.75H6.75C6.19772 1.75 5.75 2.19772 5.75 2.75V21.25C5.75 21.8023 6.19772 22.25 6.75 22.25Z"
                    stroke="black"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-sm font-medium">Get mobile app!</p>
                <p className="text-sm text-gray-500">
                  Better experience and convenience - in your pocket!
                </p>
              </div>
              <button
                onClick={() => setIsDownloadAppModalOpen(true)}
                className="text-sm text-blue-600 underline"
              >
                Download now
              </button>
            </div>
          </div>
        )}

        {/* Toggle Button at the bottom */}
        <div className="mt-auto border-t p-4">
          <button
            onClick={() => toggleOpen(!isOpen)}
            className="flex w-full items-center justify-center rounded-lg border bg-white p-2 text-gray-400 transition-colors hover:bg-gray-50"
          >
            <svg
              className={`h-6 w-6 transform transition-transform duration-300 ${
                !isOpen ? "rotate-180" : ""
              }`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M15 18l-6-6 6-6" />
            </svg>
          </button>
        </div>
      </div>
      {isDownloadAppModalOpen && (
        <DownloadAppModal onClose={() => setIsDownloadAppModalOpen(false)} />
      )}
    </div>
  );
};

export default UserHeader;
