import React, { createContext, useContext, useReducer, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import { useAuth } from "../Auth/AuthContext";
import { fetchRoleAccess } from "Utils/roleAccess";
import { useNavigate } from "react-router-dom";

const ClubContext = createContext();
const sdk = new MkdSDK();
const tdk = new TreeSDK();

const initialState = {
  club: null,
  sports: [],
  courts: [],
  coach_profile: null,
  user_profile: null,
  staff_profile: null,
  staff_access: null,
  club_membership: [],
  user_subscription: null,
  user_permissions: null,
  loading: false,
  error: null,
  pricing: [],
  club_permissions: null,
  refetchTrigger: 0,
};

const clubReducer = (state, action) => {
  switch (action.type) {
    case "SET_LOADING":
      return {
        ...state,
        loading: action.payload,
      };
    case "SET_CLUB":
      return {
        ...state,
        club: action.payload,
        loading: false,
      };
    case "SET_SPORTS":
      return {
        ...state,
        sports: action.payload,
        loading: false,
      };
    case "SET_PRICING":
      return {
        ...state,
        pricing: action.payload,
        loading: false,
      };
    case "SET_COURTS":
      return {
        ...state,
        courts: action.payload,
        loading: false,
      };
    case "SET_COACH_PROFILE":
      return {
        ...state,
        coach_profile: action.payload,
        loading: false,
      };
    case "SET_STAFF_PROFILE":
      return {
        ...state,
        staff_profile: action.payload,
        loading: false,
      };
    case "SET_USER_PROFILE":
      return {
        ...state,
        user_profile: action.payload,
        loading: false,
      };
    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case "SET_CLUB_MEMBERSHIP":
      return {
        ...state,
        club_membership: action.payload,
      };
    case "SET_USER_SUBSCRIPTION":
      return {
        ...state,
        user_subscription: action.payload,
      };
    case "SET_USER_PERMISSIONS":
      return {
        ...state,
        user_permissions: action.payload,
      };

    case "SET_CLUB_PERMISSIONS":
      return {
        ...state,
        club_permissions: action.payload,
        staff_access: action.payload,
      };

    case "TRIGGER_REFETCH":
      return {
        ...state,
        refetchTrigger: state.refetchTrigger + 1,
      };

    default:
      return state;
  }
};

export const ClubProvider = ({ children }) => {
  const [state, dispatch] = useReducer(clubReducer, initialState);
  const userRole = localStorage.getItem("role");
  const { state: authState } = useAuth();
  const navigate = useNavigate();

  const setUserPermissions = (subscription, membershipSettings) => {
    if (!subscription || !membershipSettings) return;

    try {
      const parsedMembership = Array.isArray(membershipSettings)
        ? membershipSettings
        : JSON.parse(membershipSettings);

      const userPlan = parsedMembership.find(
        (plan) => plan.plan_id === subscription.planId
      );

      if (!userPlan) return;

      const permissions = {
        planName: userPlan.plan_name,
        planPrice: userPlan.price,
        allowClinic: userPlan.allow_clinic,
        allowBuddy: userPlan.allow_buddy,
        allowCoach: userPlan.allow_coach,
        allowGroups: userPlan.allow_groups,
        allowCourt: userPlan.allow_court,
        features: userPlan.features,
        subscriptionId: subscription.subId,
        stripeUid: subscription.stripe_uid,
        ...userPlan,
      };

      dispatch({ type: "SET_USER_PERMISSIONS", payload: permissions });
    } catch (error) {
      console.error("Error setting user permissions:", error);
      dispatch({
        type: "SET_ERROR",
        payload: "Error setting user permissions",
      });
    }
  };

  const user_id = localStorage.getItem("user");
  const fetchClubData = async () => {
    if (!user_id || !authState.isAuthenticated) return;
    dispatch({ type: "SET_LOADING", payload: true });
    try {
      let clubId;
      let userSubscription;

      // First fetch user subscription if user role
      if (userRole === "user") {
        try {
          const clubResponse = await sdk.callRawAPI(
            `/v3/api/custom/courtmatchup/user/club/${clubId}`,
            {},
            "GET"
          );
          const subscriptionResult = await sdk.getCustomerStripeSubscription(
            user_id
          );
          userSubscription = subscriptionResult?.customer;
          dispatch({
            type: "SET_USER_SUBSCRIPTION",
            payload: subscriptionResult.customer,
          });

          const membershipSettings = clubResponse?.model?.membership_settings
            ? JSON.parse(clubResponse.model.membership_settings)
            : [];

          dispatch({
            type: "SET_CLUB_MEMBERSHIP",
            payload: membershipSettings,
          });

          // Check for subscription and redirect if needed

          if (
            subscriptionResult.customer !== null &&
            subscriptionResult.customer !== undefined
          ) {
            // Check if both subId and planId are null
            if (
              subscriptionResult.customer.subId === null &&
              subscriptionResult.customer.planId === null &&
              membershipSettings.length >= 1
            ) {
              navigate("/user/membership/buy");
            }
          }
        } catch (error) {
          console.error("Error fetching user subscription:", error);
          dispatch({ type: "SET_ERROR", payload: error.message });
        }
      }

      // Continue with existing club data fetching
      if (userRole === "coach") {
        const result = await sdk.callRawAPI(
          `/v3/api/custom/courtmatchup/coach/profile`
        );
        clubId = result.club_id;
        dispatch({ type: "SET_COACH_PROFILE", payload: result });
      } else if (userRole === "user") {
        const userResponse = await tdk.getOne("user", user_id, {});
        clubId = userResponse.model.club_id;
        dispatch({ type: "SET_USER_PROFILE", payload: userResponse.model });
      } else if (userRole === "club") {
        clubId = user_id;
      } else if (userRole === "staff") {
        const staffResponse = await sdk.callRawAPI(
          `/v3/api/custom/courtmatchup/staff/profile`,
          {},
          "GET"
        );
        console.log(staffResponse);
        clubId = staffResponse.club_id;
        const roleAccess = await fetchRoleAccess(staffResponse?.club_id);
        dispatch({ type: "SET_CLUB_PERMISSIONS", payload: roleAccess?.staff });
        dispatch({ type: "SET_STAFF_PROFILE", payload: staffResponse });
      }

      if (userRole === "club") {
        const clubResponse = await sdk.callRawAPI(
          `/v3/api/custom/courtmatchup/club/profile`,
          {},
          "GET"
        );
        dispatch({ type: "SET_CLUB", payload: clubResponse.model.club });
        dispatch({ type: "SET_SPORTS", payload: clubResponse.model.sports });
        dispatch({ type: "SET_COURTS", payload: clubResponse.model.courts });
        dispatch({ type: "SET_PRICING", payload: clubResponse.model.pricing });
      } else {
        const clubResponse = await sdk.callRawAPI(
          `/v3/api/custom/courtmatchup/user/club/${clubId}`,
          {},
          "GET"
        );

        dispatch({ type: "SET_CLUB", payload: clubResponse.model });

        const membershipSettings = clubResponse?.model?.membership_settings
          ? JSON.parse(clubResponse.model.membership_settings)
          : [];
        dispatch({
          type: "SET_CLUB_MEMBERSHIP",
          payload: membershipSettings,
        });
        dispatch({ type: "SET_SPORTS", payload: clubResponse.sports });
        dispatch({ type: "SET_COURTS", payload: clubResponse.courts });
        dispatch({ type: "SET_PRICING", payload: clubResponse.pricing });
        // Set user permissions after both subscription and membership data are available
        if (userRole === "user" && userSubscription) {
          setUserPermissions(userSubscription, membershipSettings);
        }
      }
    } catch (error) {
      console.error("Error fetching club data:", error);
      dispatch({ type: "SET_ERROR", payload: error.message });
    }
  };

  const triggerRefetch = () => {
    dispatch({ type: "TRIGGER_REFETCH" });
  };

  useEffect(() => {
    if (user_id && authState.isAuthenticated) {
      fetchClubData();
    } else {
      dispatch({ type: "SET_CLUB", payload: null });
      dispatch({ type: "SET_SPORTS", payload: [] });
      dispatch({ type: "SET_PRICING", payload: [] });
      dispatch({ type: "SET_COURTS", payload: [] });
      dispatch({ type: "SET_COACH_PROFILE", payload: null });
      dispatch({ type: "SET_USER_PROFILE", payload: null });
      dispatch({ type: "SET_STAFF_PROFILE", payload: null });
      dispatch({ type: "SET_CLUB_MEMBERSHIP", payload: [] });
      dispatch({ type: "SET_USER_SUBSCRIPTION", payload: null });
      dispatch({ type: "SET_USER_PERMISSIONS", payload: null });
      dispatch({ type: "SET_CLUB_PERMISSIONS", payload: null });
    }
  }, [user_id, authState.isAuthenticated, state.refetchTrigger]);

  const value = {
    ...state,
    fetchClubData,
    setUserPermissions,
    triggerRefetch,
  };

  return <ClubContext.Provider value={value}>{children}</ClubContext.Provider>;
};

export const useClub = () => {
  const context = useContext(ClubContext);
  if (context === undefined) {
    throw new Error("useClub must be used within a ClubProvider");
  }
  return context;
};

export default ClubContext;
